---
type: "always_apply"
---

# Figma还原调试快速参考

## 🚨 三步调试法

### 1️⃣ 还原度诊断
```bash
node scripts/figma-diff-analyzer.js ComponentName
```

| 还原度 | 问题类型 | 优先级 | 解决方案 |
|--------|----------|--------|----------|
| < 90% | 素材问题 | 🔴 最高 | 重新下载Figma素材 |
| 90-95% | 布局问题 | 🟡 中等 | 检查定位和尺寸 |
| > 95% | 细节优化 | 🟢 最低 | 微调或忽略 |

### 2️⃣ 差异模式识别
- **四象限差异** → 背景素材问题
- **单行/列差异** → 布局结构问题  
- **边缘细微差异** → 字体渲染差异（可忽略）

### 3️⃣ 快速修复
```bash
# 素材重新下载
download_figma_images_figma-local

# 布局精确定位
基于Figma boundingBox数据

# 验证效果
node scripts/fix-screenshot-dimensions.js fix ComponentName
```

## 💡 黄金法则

### ✅ 优先选择
- 背景图 > 复杂CSS结构
- Flex布局 > 绝对定位
- Figma素材 > 代码模拟

### ❌ 避免陷阱
- 过度使用绝对定位
- 复杂的多层DOM结构
- 凭感觉调整位置

## 🎯 目标标准
- **97%+**: 优秀级别 ✨
- **95%+**: 生产可用 ✅
- **90%+**: 需要优化 ⚠️
- **<90%**: 重新检查 ❌

---
*基于ExchangeSuccess还原经验总结 - 2025.07.14*
