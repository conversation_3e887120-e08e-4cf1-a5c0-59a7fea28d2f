import fs from 'fs/promises';
import path from 'path';
import chalk from 'chalk';
import { EnhancedCompareImagesTool } from './enhanced-compare-images.js';

export class FigmaDiffAnalyzerTool {
  constructor() {
    this.description = 'Complete Figma restoration analysis with debugging and fix suggestions';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to analyze'
        },
        projectPath: {
          type: 'string',
          default: '/Users/<USER>/Documents/work/camscanner-cloud-vue3',
          description: 'Path to the Vue project'
        },
        generateReport: {
          type: 'boolean',
          default: true,
          description: 'Generate detailed Markdown report'
        },
        threshold: {
          type: 'number',
          default: 0.1,
          description: 'Comparison threshold'
        }
      },
      required: ['componentName']
    };
  }

  async execute(args) {
    const {
      componentName,
      projectPath = '/Users/<USER>/Documents/work/camscanner-cloud-vue3',
      generateReport = true,
      threshold = 0.1
    } = args;

    try {
      console.log(chalk.cyan('🎯 Figma Restoration Analysis'));
      console.log(chalk.cyan(`Component: ${componentName}`));
      console.log(chalk.gray('='.repeat(60)));

      const resultsDir = path.join(projectPath, 'mcp-vue-tools', 'results', componentName);

      // Step 1: Enhanced image comparison
      console.log(chalk.yellow('\n📊 Step 1: Enhanced Image Comparison'));
      console.log(chalk.gray('-'.repeat(30)));

      const compareImagesTool = new EnhancedCompareImagesTool();
      const compareResult = await compareImagesTool.execute({
        componentName,
        projectPath,
        threshold,
        enableDebugging: true
      });

      if (!compareResult.success) {
        throw new Error(`Image comparison failed: ${compareResult.error}`);
      }

      // Step 2: Load Figma data for element matching
      console.log(chalk.yellow('\n🎯 Step 2: Figma Element Matching'));
      console.log(chalk.gray('-'.repeat(30)));

      const figmaMatches = await this.matchFigmaElements(compareResult, resultsDir);

      // Step 3: Generate fix suggestions
      console.log(chalk.yellow('\n🔧 Step 3: Smart Fix Suggestions'));
      console.log(chalk.gray('-'.repeat(30)));

      const fixSuggestions = this.generateFixSuggestions(compareResult, figmaMatches);

      // Step 4: Create comprehensive analysis
      const analysisReport = this.generateAnalysisReport(compareResult, figmaMatches, fixSuggestions);

      // Step 5: Save reports
      if (generateReport) {
        await this.saveReports(analysisReport, resultsDir);
      }

      // Step 6: Print summary
      this.printSummary(analysisReport);

      return analysisReport;

    } catch (error) {
      console.error(chalk.red('❌ Analysis failed:'), error.message);
      return {
        success: false,
        error: error.message,
        componentName
      };
    }
  }

  async matchFigmaElements(compareResult, resultsDir) {
    try {
      const figmaDataPath = path.join(resultsDir, 'complete-figma-data.json');
      const figmaDataExists = await this.fileExists(figmaDataPath);

      if (!figmaDataExists) {
        console.log(chalk.yellow('⚠️ Figma data not found, skipping element matching'));
        return [];
      }

      const figmaDataContent = await fs.readFile(figmaDataPath, 'utf-8');
      const figmaData = JSON.parse(figmaDataContent);

      const matches = [];
      const diffRegions = compareResult.debugInfo?.diffRegions || [];

      for (let i = 0; i < diffRegions.length; i++) {
        const region = diffRegions[i];
        const matchedElements = this.findMatchingFigmaElements(region, figmaData);
        
        if (matchedElements.length > 0) {
          matches.push({
            regionId: i + 1,
            region,
            elements: matchedElements
          });
          console.log(chalk.green(`✅ Region ${i + 1}: Found ${matchedElements.length} matching elements`));
        } else {
          console.log(chalk.yellow(`⚠️ Region ${i + 1}: No matching Figma elements found`));
        }
      }

      return matches;

    } catch (error) {
      console.log(chalk.yellow(`⚠️ Figma element matching failed: ${error.message}`));
      return [];
    }
  }

  findMatchingFigmaElements(region, figmaData) {
    const matches = [];
    
    // Convert 1x coordinates to 3x for comparison (assuming 3x scale images)
    const scale = 3;
    const regionBounds = {
      left: region.left / scale,
      top: region.top / scale,
      right: region.right / scale,
      bottom: region.bottom / scale
    };

    const searchNodes = (nodes) => {
      if (!nodes) return;

      for (const node of nodes) {
        if (node.boundingBox) {
          const overlap = this.calculateOverlap(regionBounds, node.boundingBox);
          if (overlap > 0.3) { // 30% overlap threshold
            matches.push({
              id: node.id,
              name: node.name,
              type: node.type,
              boundingBox: node.boundingBox,
              overlapPercentage: overlap * 100,
              confidence: this.calculateConfidence(overlap, regionBounds, node.boundingBox)
            });
          }
        }

        if (node.children) {
          searchNodes(node.children);
        }
      }
    };

    if (figmaData.nodes) {
      searchNodes(figmaData.nodes);
    }

    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  calculateOverlap(region, boundingBox) {
    const left = Math.max(region.left, boundingBox.x);
    const top = Math.max(region.top, boundingBox.y);
    const right = Math.min(region.right, boundingBox.x + boundingBox.width);
    const bottom = Math.min(region.bottom, boundingBox.y + boundingBox.height);

    if (left >= right || top >= bottom) return 0;

    const overlapArea = (right - left) * (bottom - top);
    const regionArea = (region.right - region.left) * (region.bottom - region.top);
    
    return overlapArea / regionArea;
  }

  calculateConfidence(overlap, region, boundingBox) {
    const overlapScore = overlap;
    const sizeScore = Math.min(1, Math.min(
      boundingBox.width / (region.right - region.left),
      boundingBox.height / (region.bottom - region.top)
    ));
    
    return (overlapScore * 0.7 + sizeScore * 0.3) * 100;
  }

  generateFixSuggestions(compareResult, figmaMatches) {
    const suggestions = [];
    const { debugInfo } = compareResult;

    if (!debugInfo) return suggestions;

    const { diagnosis, diffRegions } = debugInfo;

    // Generate suggestions based on debugging principles
    switch (diagnosis.category) {
      case 'material_issue':
        suggestions.push({
          priority: 'high',
          type: 'material',
          message: '重新下载Figma素材',
          description: '检测到大区域差异，可能是素材或图标问题',
          fixes: [
            '使用正确的节点ID重新下载所有素材',
            '验证图片路径和文件名是否正确',
            '考虑使用背景图替代复杂CSS结构',
            '检查图片格式和尺寸是否匹配'
          ]
        });
        break;

      case 'layout_issue':
        for (const match of figmaMatches) {
          if (match.elements.length > 0) {
            const element = match.elements[0];
            suggestions.push({
              priority: 'medium',
              type: 'layout',
              message: `调整 ${element.name} 的位置`,
              description: `元素位置可能不准确，置信度: ${element.confidence.toFixed(1)}%`,
              coordinates: `Figma: (${element.boundingBox.x}, ${element.boundingBox.y}) ${element.boundingBox.width}×${element.boundingBox.height}`,
              fixes: [
                `position: absolute; top: ${element.boundingBox.y}px; left: ${element.boundingBox.x}px;`,
                `width: ${element.boundingBox.width}px; height: ${element.boundingBox.height}px;`,
                '检查父容器的定位和尺寸',
                '验证z-index层级关系'
              ]
            });
          }
        }
        break;

      case 'font_rendering':
        suggestions.push({
          priority: 'low',
          type: 'font',
          message: '字体渲染差异',
          description: '当前还原度已达到优秀标准，字体差异可忽略',
          fixes: [
            '当前还原度已达到生产标准',
            '字体渲染差异属于正常现象',
            '如需优化，可微调font-weight和line-height',
            '建议保持当前实现'
          ]
        });
        break;
    }

    return suggestions;
  }

  generateAnalysisReport(compareResult, figmaMatches, fixSuggestions) {
    const { componentName, matchPercentage, debugInfo } = compareResult;
    
    return {
      componentName,
      timestamp: new Date().toISOString(),
      comparison: {
        matchPercentage,
        diffPixels: compareResult.diffPixels,
        totalPixels: compareResult.totalPixels,
        dimensions: compareResult.dimensions,
        qualityLevel: debugInfo?.qualityLevel
      },
      diffAnalysis: {
        totalRegions: debugInfo?.diffRegions?.length || 0,
        regions: debugInfo?.diffRegions || [],
        diagnosis: debugInfo?.diagnosis
      },
      figmaMatching: {
        totalMatches: figmaMatches.length,
        matches: figmaMatches
      },
      fixSuggestions: {
        totalSuggestions: fixSuggestions.length,
        suggestions: fixSuggestions,
        priorityBreakdown: this.getPriorityBreakdown(fixSuggestions)
      },
      summary: {
        status: debugInfo?.qualityLevel || { level: 'unknown', emoji: '❓', text: '未知' },
        recommendation: this.getRecommendation(matchPercentage, fixSuggestions),
        nextSteps: this.getNextSteps(matchPercentage, fixSuggestions)
      }
    };
  }

  getPriorityBreakdown(suggestions) {
    return {
      high: suggestions.filter(s => s.priority === 'high').length,
      medium: suggestions.filter(s => s.priority === 'medium').length,
      low: suggestions.filter(s => s.priority === 'low').length
    };
  }

  getRecommendation(matchPercentage, suggestions) {
    if (matchPercentage >= 97) {
      return '组件还原度已达到优秀标准，可以投入使用';
    }
    
    const highPriority = suggestions.filter(s => s.priority === 'high').length;
    if (highPriority > 0) {
      return `优先修复 ${highPriority} 个高优先级问题，预计可提升还原度`;
    }
    
    return '继续优化布局和样式，逐步提升还原度';
  }

  getNextSteps(matchPercentage, suggestions) {
    const steps = [];
    
    const highPriority = suggestions.filter(s => s.priority === 'high').length;
    if (highPriority > 0) {
      steps.push('立即修复所有高优先级问题');
      steps.push('重新截图并验证效果');
    }
    
    if (matchPercentage < 97) {
      steps.push('继续迭代优化直到达到97%+还原度');
    }
    
    steps.push('更新组件文档和使用说明');
    
    return steps;
  }

  async saveReports(analysisReport, resultsDir) {
    // Save JSON report
    const jsonPath = path.join(resultsDir, 'figma-analysis-report.json');
    await fs.writeFile(jsonPath, JSON.stringify(analysisReport, null, 2));

    // Generate and save Markdown report
    const markdownReport = this.generateMarkdownReport(analysisReport);
    const markdownPath = path.join(resultsDir, 'figma-analysis-report.md');
    await fs.writeFile(markdownPath, markdownReport);

    console.log(chalk.gray(`💾 Reports saved:`));
    console.log(chalk.gray(`   JSON: ${jsonPath}`));
    console.log(chalk.gray(`   Markdown: ${markdownPath}`));
  }

  generateMarkdownReport(report) {
    const { componentName, comparison, diffAnalysis, figmaMatching, fixSuggestions, summary } = report;
    
    return `# ${componentName} Figma还原分析报告

## 📊 总体评估

- **还原度**: ${comparison.matchPercentage}%
- **状态**: ${summary.status.emoji} ${summary.status.text}
- **发现问题**: ${fixSuggestions.totalSuggestions} 个
- **生成时间**: ${new Date(report.timestamp).toLocaleString('zh-CN')}

## 🎯 问题分布

| 优先级 | 数量 | 说明 |
|--------|------|------|
| 🔴 高优先级 | ${fixSuggestions.priorityBreakdown.high} | 需要立即修复 |
| 🟡 中优先级 | ${fixSuggestions.priorityBreakdown.medium} | 建议优化 |
| 🟢 低优先级 | ${fixSuggestions.priorityBreakdown.low} | 可选优化 |

## 🔍 差异区域分析

发现 ${diffAnalysis.totalRegions} 个差异区域

${diffAnalysis.regions.map((region, index) => `
### 区域 ${index + 1}
- **位置**: (${region.left}, ${region.top}) → (${region.right}, ${region.bottom})
- **尺寸**: ${region.width} × ${region.height}
- **像素数**: ${region.pixelCount}
`).join('')}

## 🔧 修复建议

${fixSuggestions.suggestions.map((suggestion, index) => `
### ${index + 1}. [${suggestion.priority.toUpperCase()}] ${suggestion.message}

${suggestion.description}

**修复方案**:
${suggestion.fixes.map(fix => `- ${fix}`).join('\n')}
`).join('')}

## 💡 总结建议

${summary.recommendation}

## 📋 下一步行动

${summary.nextSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}

---
*报告生成时间: ${new Date(report.timestamp).toLocaleString('zh-CN')}*
`;
  }

  printSummary(report) {
    const { comparison, summary, fixSuggestions } = report;
    
    console.log(chalk.cyan('\n🎉 Analysis Complete!'));
    console.log(chalk.white(`📊 Match: ${comparison.matchPercentage}% ${summary.status.emoji}`));
    console.log(chalk.white(`🔍 Issues: ${fixSuggestions.totalSuggestions}`));
    console.log(chalk.white(`💡 Recommendation: ${summary.recommendation}`));
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
