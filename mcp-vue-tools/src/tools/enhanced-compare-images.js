import fs from 'fs/promises';
import path from 'path';
import { PNG } from 'pngjs';
import pixelmatch from 'pixelmatch';
import chalk from 'chalk';

export class EnhancedCompareImagesTool {
  constructor() {
    this.description = 'Enhanced image comparison with debugging features and Figma restoration analysis';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to analyze'
        },
        expectedPath: {
          type: 'string',
          description: 'Path to expected image (Figma design)'
        },
        actualPath: {
          type: 'string',
          description: 'Path to actual image (screenshot)'
        },
        diffPath: {
          type: 'string',
          description: 'Path where diff image should be saved'
        },
        threshold: {
          type: 'number',
          default: 0.1,
          minimum: 0,
          maximum: 1,
          description: 'Matching threshold (0-1, lower is more strict)'
        },
        includeAA: {
          type: 'boolean',
          default: false,
          description: 'Whether to include anti-aliasing in comparison'
        },
        enableDebugging: {
          type: 'boolean',
          default: true,
          description: 'Enable enhanced debugging features'
        },
        projectPath: {
          type: 'string',
          default: '/Users/<USER>/Documents/work/camscanner-cloud-vue3',
          description: 'Path to the Vue project'
        }
      },
      required: ['componentName']
    };
  }

  async execute(args) {
    const {
      componentName,
      expectedPath,
      actualPath,
      diffPath,
      threshold = 0.1,
      includeAA = false,
      enableDebugging = true,
      projectPath = '/Users/<USER>/Documents/work/camscanner-cloud-vue3'
    } = args;

    try {
      console.log(chalk.cyan('🔍 Enhanced Figma Restoration Analysis'));
      console.log(chalk.gray('='.repeat(60)));

      // Resolve paths
      const mcpResultsDir = path.join(projectPath, 'mcp-vue-tools', 'results', componentName);
      const resolvedExpectedPath = expectedPath || path.join(mcpResultsDir, `${componentName}_expected.png`);
      const resolvedActualPath = actualPath || path.join(mcpResultsDir, 'actual.png');
      const resolvedDiffPath = diffPath || path.join(mcpResultsDir, 'diff.png');

      // Check if files exist
      const expectedExists = await this.fileExists(resolvedExpectedPath);
      const actualExists = await this.fileExists(resolvedActualPath);

      if (!expectedExists) {
        throw new Error(`Expected image not found: ${resolvedExpectedPath}`);
      }

      if (!actualExists) {
        throw new Error(`Actual image not found: ${resolvedActualPath}`);
      }

      // Load and compare images
      const expectedBuffer = await fs.readFile(resolvedExpectedPath);
      const actualBuffer = await fs.readFile(resolvedActualPath);

      const expectedImage = PNG.sync.read(expectedBuffer);
      const actualImage = PNG.sync.read(actualBuffer);

      // Check dimensions
      if (expectedImage.width !== actualImage.width || expectedImage.height !== actualImage.height) {
        console.log(chalk.yellow(`📐 Dimension mismatch detected:`));
        console.log(chalk.yellow(`   Expected: ${expectedImage.width}×${expectedImage.height}`));
        console.log(chalk.yellow(`   Actual: ${actualImage.width}×${actualImage.height}`));
        
        // Auto-resize if needed (basic implementation)
        // For now, throw error - could implement smart resizing later
        throw new Error(`Image dimensions don't match. Expected: ${expectedImage.width}×${expectedImage.height}, Actual: ${actualImage.width}×${actualImage.height}`);
      }

      console.log(chalk.green(`📐 Image dimensions: ${expectedImage.width}×${expectedImage.height}`));

      // Create diff image
      const { width, height } = expectedImage;
      const diffImage = new PNG({ width, height });

      // Compare images
      const diffPixels = pixelmatch(
        expectedImage.data,
        actualImage.data,
        diffImage.data,
        width,
        height,
        { threshold, includeAA }
      );

      // Save diff image
      await fs.mkdir(path.dirname(resolvedDiffPath), { recursive: true });
      await fs.writeFile(resolvedDiffPath, PNG.sync.write(diffImage));

      // Calculate match percentage
      const totalPixels = width * height;
      const matchPercentage = ((totalPixels - diffPixels) / totalPixels * 100);

      // Enhanced debugging analysis
      let debugInfo = {};
      if (enableDebugging) {
        debugInfo = this.generateDebugInfo(matchPercentage, diffPixels, totalPixels, diffImage.data, width, height);
      }

      const result = {
        success: true,
        componentName,
        matchPercentage: parseFloat(matchPercentage.toFixed(2)),
        diffPixels,
        totalPixels,
        dimensions: { width, height },
        paths: {
          expected: resolvedExpectedPath,
          actual: resolvedActualPath,
          diff: resolvedDiffPath
        },
        debugInfo
      };

      // Enhanced console output
      this.printResults(result);

      // Save detailed analysis
      if (enableDebugging) {
        await this.saveAnalysisReport(result, mcpResultsDir);
      }

      return result;

    } catch (error) {
      console.error(chalk.red('❌ Enhanced comparison failed:'), error.message);
      return {
        success: false,
        error: error.message,
        componentName
      };
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  generateDebugInfo(matchPercentage, diffPixels, totalPixels, diffData, width, height) {
    // Quality level assessment
    const qualityLevel = this.getQualityLevel(matchPercentage);
    
    // Detect diff regions
    const diffRegions = this.detectDiffRegions(diffData, width, height);
    
    // Problem diagnosis based on our debugging principles
    const diagnosis = this.diagnoseProblem(matchPercentage, diffRegions, width, height);
    
    return {
      qualityLevel,
      diffRegions,
      diagnosis,
      recommendations: this.getRecommendations(diagnosis)
    };
  }

  getQualityLevel(matchPercentage) {
    if (matchPercentage >= 99) return { level: 'perfect', emoji: '🏆', text: '完美级别' };
    if (matchPercentage >= 97) return { level: 'excellent', emoji: '✨', text: '优秀级别' };
    if (matchPercentage >= 95) return { level: 'good', emoji: '👍', text: '良好级别' };
    if (matchPercentage >= 90) return { level: 'needs_improvement', emoji: '⚠️', text: '需要改进' };
    return { level: 'poor', emoji: '❌', text: '需要重新检查' };
  }

  detectDiffRegions(diffData, width, height, threshold = 100) {
    let diffPixelCount = 0;
    let minX = width, maxX = 0, minY = height, maxY = 0;
    
    // Scan for diff pixels (red pixels in diff image)
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const idx = (width * y + x) << 2;
        const r = diffData[idx];
        const g = diffData[idx + 1];
        const b = diffData[idx + 2];
        
        if (r > 200 && g < 50 && b < 50) {
          diffPixelCount++;
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);
        }
      }
    }
    
    if (diffPixelCount === 0) return [];
    
    // Create regions (simplified quadrant approach)
    const regions = [];
    const regionWidth = maxX - minX + 1;
    const regionHeight = maxY - minY + 1;
    
    // If diff area is large, split into quadrants for analysis
    if (regionWidth > 300 || regionHeight > 300) {
      const midX = Math.floor((minX + maxX) / 2);
      const midY = Math.floor((minY + maxY) / 2);
      
      const quadrants = [
        { left: minX, top: minY, right: midX, bottom: midY, name: 'top-left' },
        { left: midX, top: minY, right: maxX, bottom: midY, name: 'top-right' },
        { left: minX, top: midY, right: midX, bottom: maxY, name: 'bottom-left' },
        { left: midX, top: midY, right: maxX, bottom: maxY, name: 'bottom-right' }
      ];
      
      for (const quad of quadrants) {
        const pixelCount = this.countPixelsInRegion(diffData, width, height, quad);
        if (pixelCount >= threshold) {
          regions.push({
            ...quad,
            width: quad.right - quad.left + 1,
            height: quad.bottom - quad.top + 1,
            pixelCount,
            center: {
              x: Math.round((quad.left + quad.right) / 2),
              y: Math.round((quad.top + quad.bottom) / 2)
            }
          });
        }
      }
    } else {
      regions.push({
        left: minX,
        right: maxX,
        top: minY,
        bottom: maxY,
        width: regionWidth,
        height: regionHeight,
        pixelCount: diffPixelCount,
        center: {
          x: Math.round((minX + maxX) / 2),
          y: Math.round((minY + maxY) / 2)
        },
        name: 'single-region'
      });
    }
    
    return regions.sort((a, b) => b.pixelCount - a.pixelCount);
  }

  countPixelsInRegion(diffData, width, height, region) {
    let count = 0;
    for (let y = region.top; y <= region.bottom && y < height; y++) {
      for (let x = region.left; x <= region.right && x < width; x++) {
        const idx = (width * y + x) << 2;
        const r = diffData[idx];
        const g = diffData[idx + 1];
        const b = diffData[idx + 2];
        
        if (r > 200 && g < 50 && b < 50) {
          count++;
        }
      }
    }
    return count;
  }

  diagnoseProblem(matchPercentage, diffRegions, width, height) {
    // Apply our debugging principles
    if (matchPercentage < 90 && diffRegions.length >= 4) {
      return {
        type: 'large_area_diff',
        priority: 'high',
        description: '大区域差异 - 素材问题',
        category: 'material_issue',
        principle: '调试原则1: 大区域差异检测'
      };
    }
    
    if (matchPercentage >= 90 && matchPercentage < 95) {
      return {
        type: 'layout_diff',
        priority: 'medium',
        description: '普通元素差异 - 布局和样式问题',
        category: 'layout_issue',
        principle: '调试原则2: 普通元素差异'
      };
    }
    
    if (matchPercentage >= 95) {
      return {
        type: 'minor_diff',
        priority: 'low',
        description: '字体差异 - 可忽略',
        category: 'font_rendering',
        principle: '调试原则3: 字体差异'
      };
    }
    
    return {
      type: 'unknown',
      priority: 'medium',
      description: '未知类型差异',
      category: 'unknown',
      principle: '需要进一步分析'
    };
  }

  getRecommendations(diagnosis) {
    const recommendations = {
      material_issue: [
        '🔍 重新下载所有Figma素材和图标',
        '📐 检查背景图和装饰元素是否正确',
        '🎯 验证节点ID和下载路径',
        '💡 考虑使用背景图替代复杂CSS结构'
      ],
      layout_issue: [
        '📏 基于Figma boundingBox精确定位元素',
        '🔧 检查flex布局设置和对齐方式',
        '🎨 验证颜色、渐变和尺寸设置',
        '📐 确认边距、内边距和边框设置'
      ],
      font_rendering: [
        '✅ 当前还原度已达到优秀标准',
        '🎯 可以投入生产使用',
        '📝 字体渲染差异属于正常现象',
        '🔍 如需进一步优化，可微调字体设置'
      ],
      unknown: [
        '🔍 运行详细的差异分析工具',
        '📊 检查图片尺寸、格式和质量',
        '🛠️ 验证组件实现逻辑和结构',
        '📞 查阅调试文档或寻求技术支持'
      ]
    };
    
    return recommendations[diagnosis.category] || recommendations.unknown;
  }

  printResults(result) {
    const { matchPercentage, diffPixels, totalPixels, dimensions, debugInfo } = result;
    
    console.log(chalk.cyan('\n📊 Analysis Results:'));
    console.log(chalk.white(`   Match: ${matchPercentage}%`));
    console.log(chalk.white(`   Diff pixels: ${diffPixels}/${totalPixels}`));
    console.log(chalk.white(`   Dimensions: ${dimensions.width}×${dimensions.height}`));
    
    if (debugInfo) {
      console.log(chalk.white(`   Quality: ${debugInfo.qualityLevel.emoji} ${debugInfo.qualityLevel.text}`));
      console.log(chalk.white(`   Diff regions: ${debugInfo.diffRegions.length}`));
      console.log(chalk.white(`   Diagnosis: ${debugInfo.diagnosis.description}`));
    }
  }

  async saveAnalysisReport(result, resultsDir) {
    const reportPath = path.join(resultsDir, 'enhanced-analysis-report.json');
    await fs.writeFile(reportPath, JSON.stringify(result, null, 2));
    console.log(chalk.gray(`💾 Analysis report saved: ${reportPath}`));
  }
}
