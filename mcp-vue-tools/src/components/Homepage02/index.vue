<template>
  <div class="homepage-container">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-content">
        <div class="navbar-right">
          <div class="user-info">
            <div class="help-icon">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M9 0C4.03 0 0 4.03 0 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm1 16H8v-2h2v2zm2.07-7.75l-.9.92C10.5 9.8 10 10.5 10 12H8v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H5c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z" fill="#595959"/>
              </svg>
            </div>
            <span class="username">蜜蜂家校校园版</span>
          </div>
          <div class="user-avatar">
            <div class="avatar-container">
              <div class="avatar-bg"></div>
              <span class="avatar-text">蜜</span>
            </div>
            <svg class="dropdown-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path d="M4.44 5.78L8 9.33l3.56-3.55" stroke="#595959" stroke-width="1.5" fill="none"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧导航栏 -->
      <div class="sidebar">
        <div class="nav-items">
          <div class="nav-item active">
            <div class="nav-icon">
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                <circle cx="10" cy="10" r="8.31" fill="#04AA65"/>
              </svg>
            </div>
            <span class="nav-text">首页</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 2h12v12H2V2z" stroke="#595959" stroke-width="1.5" fill="none"/>
              </svg>
            </div>
            <span class="nav-text">题库</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M2 2h12v12H2V2z" stroke="#595959" stroke-width="1.5" fill="none"/>
              </svg>
            </div>
            <span class="nav-text">作业</span>
          </div>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="divider"></div>

      <!-- 中间内容区域 -->
      <div class="content-area">
        <!-- 题库组卷模块 -->
        <div class="module-card">
          <div class="module-header">
            <h2 class="module-title">题库组卷</h2>
          </div>
          <div class="module-content">
            <div class="feature-card">
              <div class="feature-icon-container">
                <div class="feature-icon-bg orange"></div>
                <div class="feature-icon">
                  <img src="./images/icon-textbook.svg" alt="校本教辅" />
                </div>
              </div>
              <div class="feature-info">
                <h3 class="feature-title">校本教辅</h3>
                <p class="feature-desc">学校数字化教辅</p>
              </div>
            </div>
            <div class="feature-card opacity-0">
              <div class="feature-icon-container">
                <div class="feature-icon-bg yellow"></div>
                <div class="feature-icon">
                  <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                    <rect x="6.67" y="5" width="6.67" height="30" fill="#FFC53D" rx="3"/>
                    <rect x="13.33" y="5" width="20" height="30" fill="#FAAD14" rx="3"/>
                    <path d="M15.66 13.33h13.33v12.5H15.66z" fill="white"/>
                  </svg>
                </div>
              </div>
              <div class="feature-info">
                <h3 class="feature-title">我的题库</h3>
                <p class="feature-desc">用户可个性化管理和存储自己收集、创建的...</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 智慧练习模块 -->
        <div class="module-card">
          <div class="module-header">
            <h2 class="module-title">智慧练习</h2>
          </div>
          <div class="module-content">
            <div class="feature-card">
              <div class="feature-icon-container">
                <div class="feature-icon-bg teal"></div>
                <div class="feature-icon">
                  <img src="./images/icon-practice.svg" alt="练习管理" />
                </div>
              </div>
              <div class="feature-info">
                <h3 class="feature-title">练习管理</h3>
                <p class="feature-desc">练习的提交、批改、发布、学情</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧卡片区域 -->
      <div class="right-card">
        <div class="card-container">
          <div class="card-background">
            <img src="./images/card-bg-only.png" alt="卡片背景" class="bg-image" />
            <div class="card-header-decoration">
              <img src="./images/card-header-only.png" alt="卡片头部装饰" class="header-decoration" />
            </div>
          </div>
          <div class="card-content">
            <div class="content-section">
              <div class="section-item">
                <span class="item-label">智慧练习系统全新升级</span>
                <div class="item-actions">
                  <span class="action-text">立即体验</span>
                  <svg class="action-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path d="M2.57 0.91L8 6.33l-5.43 5.42" stroke="#BFBFBF" stroke-width="1" fill="none"/>
                  </svg>
                </div>
              </div>
              <div class="section-item">
                <span class="item-label">练习管理功能优化</span>
                <div class="item-actions">
                  <span class="action-text">立即体验</span>
                  <svg class="action-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
                    <path d="M2.57 0.91L8 6.33l-5.43 5.42" stroke="#BFBFBF" stroke-width="1" fill="none"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 组件状态
const activeNavItem = ref('首页')

// 方法
const handleNavClick = (item) => {
  activeNavItem.value = item
}

const handleFeatureClick = (feature) => {
  console.log('点击功能:', feature)
}
</script>

<style scoped>
.homepage-container {
  width: 1440px !important;
  height: 800px !important;
  background: #FFFFFF;
  position: relative;
  font-family: 'PingFang SC', sans-serif;
  overflow: hidden;
  box-sizing: border-box;
  min-width: 1440px;
  max-width: 1440px;
}

/* 顶部导航栏 */
.top-navbar {
  width: 100%;
  height: 64px;
  background: #FFFFFF;
  border-bottom: 1px solid #E8E8E8;
  position: relative;
}

.navbar-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 24px;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.username {
  font-size: 14px;
  font-weight: 400;
  color: #262626;
  line-height: 22px;
}

.user-avatar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  background: #C6F3E1;
  cursor: pointer;
}

.avatar-container {
  width: 18px;
  height: 18px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #39D385 0%, #65EB8A 100%);
  border-radius: 50%;
  position: absolute;
}

.avatar-text {
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
  position: relative;
  z-index: 1;
}

.dropdown-icon {
  width: 16px;
  height: 16px;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  height: 736px;
  position: relative;
  width: 100%;
}

/* 左侧导航栏 */
.sidebar {
  width: 72px;
  height: 100%;
  background: #FFFFFF;
  border-right: 1px solid #E8E8E8;
  padding: 29px 24px;
  display: flex;
  flex-direction: column;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 24px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.nav-item.active .nav-icon svg circle {
  fill: #04AA65;
}

.nav-item.active .nav-text {
  color: #04AA65;
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
  color: #595959;
  line-height: 14px;
  text-align: center;
}

/* 分割线 */
.divider {
  width: 1px;
  height: 100%;
  background: #E8E8E8;
}

/* 中间内容区域 */
.content-area {
  width: 1007px;
  padding: 80px 90px 80px 90px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.module-card {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.module-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.module-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 28px;
  margin: 0;
}

.module-content {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  width: 928px;
}

.feature-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  height: 80px;
  flex: 1;
  min-width: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.feature-card:hover {
  background: #F5F5F5;
  border-radius: 8px;
}

.feature-card.opacity-0 {
  opacity: 0;
}

.feature-icon-container {
  width: 48px;
  height: 48px;
  position: relative;
  flex-shrink: 0;
}

.feature-icon-bg {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  opacity: 0.1;
  position: absolute;
  top: 0;
  left: 0;
}

.feature-icon-bg.orange {
  background: #FA8C16;
}

.feature-icon-bg.yellow {
  background: #FAAD14;
}

.feature-icon-bg.teal {
  background: #13C2C2;
}

.feature-icon {
  width: 40px;
  height: 40px;
  position: absolute;
  top: 4px;
  left: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.feature-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0;
}

.feature-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 24px;
  margin: 0 0 2px 0;
}

.feature-desc {
  font-size: 14px;
  font-weight: 400;
  color: #8C8C8C;
  line-height: 24px;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧卡片区域 */
.right-card {
  width: 344px;
  height: 456px;
  position: absolute;
  top: 80px;
  right: 16px;
  flex-shrink: 0;
}

.card-container {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.card-background {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #FA541C 0%, #FFA38F 100%);
  border-radius: 8px;
}

.bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.card-header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.header-decoration {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wave-pattern {
  position: absolute;
  top: 0;
  left: 129px;
  width: 215px;
  height: 52px;
  overflow: hidden;
}

.wave-pattern img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.notification-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 80px;
  height: 28px;
  background: linear-gradient(135deg, #FA541C 0%, #FFA38F 100%);
  border: 1px solid #FFFFFF;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 18px;
}

.card-content {
  position: absolute;
  bottom: 69px;
  left: 24px;
  right: 24px;
  background: #FBFBFB;
  border-radius: 8px;
  padding: 16px;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.item-label {
  font-size: 16px;
  font-weight: 400;
  color: #262626;
  line-height: 24px;
  flex: 1;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-text {
  font-size: 12px;
  font-weight: 400;
  color: #8C8C8C;
  line-height: 18px;
}

.action-icon {
  width: 12px;
  height: 12px;
}

/* 响应式调整 */
@media (max-width: 1440px) {
  .homepage-container {
    width: 100%;
    min-width: 1200px;
  }
  
  .content-area {
    max-width: calc(100% - 416px);
  }
  
  .module-content {
    width: 100%;
  }
}
</style>